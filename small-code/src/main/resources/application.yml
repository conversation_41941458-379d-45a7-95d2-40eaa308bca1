spring:
  application:
    name: small-code
  
  # 数据源配置 - Supabase PostgreSQL
  datasource:
    url: ***********************************************************
    username: postgres
    password: ${SUPABASE_DB_PASSWORD:your-password}
    driver-class-name: org.postgresql.Driver
    
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: isValid
      logic-delete-value: 0
      logic-not-delete-value: 1
      # 主键策略
      id-type: auto
      # 字段策略
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_empty
    # 自动填充配置
    meta-object-handler: com.small.code.config.MyMetaObjectHandler

# SpringDoc配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: 川味小厨餐饮小程序API
    description: 川味小厨餐饮小程序后端接口文档
    version: 1.0.0
    contact:
      name: 开发团队
      email: <EMAIL>

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api/v1

# 日志配置
logging:
  level:
    com.small.code: debug
    org.springframework.web: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/small-code.log

# 应用自定义配置
app:
  # JWT配置
  jwt:
    secret: ${JWT_SECRET:chuanwei-xiaochu-jwt-secret-key-2024}
    expiration: 86400000 # 24小时

  # 租户配置
  tenant:
    default-id: 1
