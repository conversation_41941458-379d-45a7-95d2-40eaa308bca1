package com.small.code.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.small.code.common.context.UserContextHolder;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis Plus 字段自动填充处理器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {
    
    /**
     * 插入时自动填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime now = LocalDateTime.now();
        
        // 自动填充创建时间
        this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, now);
        // 自动填充更新时间
        this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, now);
        // 自动填充逻辑删除字段
        this.strictInsertFill(metaObject, "isValid", Integer.class, 1);
        // 自动填充租户ID
        this.strictInsertFill(metaObject, "tenantId", Long.class, getCurrentTenantId());
    }
    
    /**
     * 更新时自动填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        // 自动填充更新时间
        this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
    }
    
    /**
     * 获取当前租户ID
     */
    private Long getCurrentTenantId() {
        return UserContextHolder.getCurrentTenantId();
    }
}
