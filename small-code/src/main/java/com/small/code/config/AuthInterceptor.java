package com.small.code.config;

import com.small.code.common.context.UserContext;
import com.small.code.common.context.UserContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 认证拦截器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class AuthInterceptor implements HandlerInterceptor {
    
    /**
     * 不需要认证的路径
     */
    private static final List<String> EXCLUDE_PATHS = Arrays.asList(
            "/swagger-ui",
            "/api-docs",
            "/v3/api-docs",
            "/swagger-resources",
            "/webjars",
            "/favicon.ico",
            "/error",
            // 公开API - 店铺信息
            "/shop/info",
            "/shop/recommended-dishes",
            // 公开API - 菜品信息
            "/dishes/categories",
            "/dishes/list",
            "/dishes/recommended",
            // 认证相关API
            "/auth/wechat-login"
    );
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        
        // 生成请求ID
        String requestId = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        
        // 记录请求日志
        log.info("Request [{}] {} {} from {}", requestId, method, requestURI, getClientIpAddress(request));
        
        // 检查是否需要认证
        if (isExcludePath(requestURI)) {
            // 设置基础上下文信息（无用户信息）
            setBasicContext(request, requestId);
            return true;
        }
        
        // TODO: 这里后续添加JWT Token验证逻辑
        // String token = extractToken(request);
        // if (token == null || !validateToken(token)) {
        //     response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        //     response.getWriter().write("{\"success\":false,\"code\":401,\"message\":\"未授权访问\"}");
        //     return false;
        // }
        
        // 暂时设置默认用户上下文（开发阶段）
        setDefaultContext(request, requestId);
        
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清除用户上下文
        UserContextHolder.clear();
    }
    
    /**
     * 检查是否为排除路径
     */
    private boolean isExcludePath(String requestURI) {
        return EXCLUDE_PATHS.stream().anyMatch(requestURI::contains);
    }
    
    /**
     * 设置基础上下文信息
     */
    private void setBasicContext(HttpServletRequest request, String requestId) {
        UserContext context = new UserContext();
        context.setTenantId(1L); // 默认租户
        context.setRequestId(requestId);
        context.setIpAddress(getClientIpAddress(request));
        context.setUserAgent(request.getHeader("User-Agent"));
        context.setIsAdmin(false);
        
        UserContextHolder.setContext(context);
    }
    
    /**
     * 设置默认用户上下文（开发阶段使用）
     */
    private void setDefaultContext(HttpServletRequest request, String requestId) {
        UserContext context = new UserContext();
        context.setUserId(1L); // 默认用户ID
        context.setTenantId(1L); // 默认租户ID
        context.setNickname("测试用户");
        context.setRequestId(requestId);
        context.setIpAddress(getClientIpAddress(request));
        context.setUserAgent(request.getHeader("User-Agent"));
        context.setIsAdmin(false);
        
        UserContextHolder.setContext(context);
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
