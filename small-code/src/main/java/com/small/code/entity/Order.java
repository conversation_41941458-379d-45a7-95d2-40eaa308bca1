package com.small.code.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

/**
 * 订单主表实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("small_orders")
@Schema(description = "订单信息")
public class Order extends BaseEntity {
    
    @Schema(description = "订单号")
    @TableField("order_number")
    private String orderNumber;
    
    @Schema(description = "用户ID")
    @TableField("user_id")
    private Long userId;
    
    @Schema(description = "桌号(数字)")
    @TableField("table_number")
    private Integer tableNumber;
    
    @Schema(description = "订单总金额")
    @TableField("total_amount")
    private BigDecimal totalAmount;
    
    @Schema(description = "优惠金额")
    @TableField("discount_amount")
    private BigDecimal discountAmount;
    
    @Schema(description = "实付金额")
    @TableField("final_amount")
    private BigDecimal finalAmount;
    
    @Schema(description = "统一状态 unpaid:待支付 paid:已支付 cooking:制作中 completed:已完成 cancelled:已取消")
    @TableField("status")
    private String status;
    
    @Schema(description = "支付方式")
    @TableField("payment_method")
    private String paymentMethod;
    
    @Schema(description = "微信预支付ID")
    @TableField("wechat_prepay_id")
    private String wechatPrepayId;
    
    @Schema(description = "微信交易号")
    @TableField("wechat_transaction_id")
    private String wechatTransactionId;
    
    @Schema(description = "订单备注")
    @TableField("remark")
    private String remark;
    
    @Schema(description = "使用的返现金额")
    @TableField("cashback_used")
    private BigDecimal cashbackUsed;
    
    @Schema(description = "支付状态 0:未支付 1:已支付 2:支付失败")
    @TableField("payment_status")
    private Integer paymentStatus;
}
