package com.small.code.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

/**
 * 菜品信息实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("small_dishes")
@Schema(description = "菜品信息")
public class Dish extends BaseEntity {
    
    @Schema(description = "分类ID")
    @TableField("category_id")
    private Long categoryId;
    
    @Schema(description = "菜品名称")
    @TableField("name")
    private String name;
    
    @Schema(description = "菜品描述")
    @TableField("description")
    private String description;
    
    @Schema(description = "价格")
    @TableField("price")
    private BigDecimal price;
    
    @Schema(description = "菜品emoji表情")
    @TableField("emoji")
    private String emoji;
    
    @Schema(description = "菜品图片URL")
    @TableField("image_url")
    private String imageUrl;
    
    @Schema(description = "库存数量 -1:无限制")
    @TableField("stock")
    private Integer stock;
    
    @Schema(description = "是否推荐 1:是 0:否")
    @TableField("is_recommended")
    private Integer isRecommended;
    
    @Schema(description = "状态 1:可售 0:下架")
    @TableField("status")
    private Integer status;
    
    @Schema(description = "排序权重")
    @TableField("sort_order")
    private Integer sortOrder;
}
