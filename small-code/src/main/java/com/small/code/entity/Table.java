package com.small.code.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 桌号管理实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("small_tables")
@Schema(description = "桌号管理")
public class Table extends BaseEntity {
    
    @Schema(description = "桌号(数字1-12)")
    @TableField("table_number")
    private Integer tableNumber;
    
    @Schema(description = "座位数")
    @TableField("seats")
    private Integer seats;
    
    @Schema(description = "状态 0:空闲 1:占用 2:清理中")
    @TableField("status")
    private Integer status;
    
    @Schema(description = "二维码内容")
    @TableField("qr_code")
    private String qrCode;
}
