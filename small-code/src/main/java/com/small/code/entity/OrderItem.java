package com.small.code.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

/**
 * 订单详情实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("small_order_items")
@Schema(description = "订单详情")
public class OrderItem extends BaseEntity {
    
    @Schema(description = "订单ID")
    @TableField("order_id")
    private Long orderId;
    
    @Schema(description = "商品类型 1:单品 2:套餐")
    @TableField("item_type")
    private Integer itemType;
    
    @Schema(description = "菜品ID")
    @TableField("dish_id")
    private Long dishId;
    
    @Schema(description = "套餐ID")
    @TableField("meal_set_id")
    private Long mealSetId;
    
    @Schema(description = "商品名称")
    @TableField("item_name")
    private String itemName;
    
    @Schema(description = "商品单价")
    @TableField("item_price")
    private BigDecimal itemPrice;
    
    @Schema(description = "数量")
    @TableField("quantity")
    private Integer quantity;
    
    @Schema(description = "小计金额")
    @TableField("subtotal")
    private BigDecimal subtotal;
}
