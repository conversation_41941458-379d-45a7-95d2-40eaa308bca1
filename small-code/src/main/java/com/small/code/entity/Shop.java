package com.small.code.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 店铺信息实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("small_shops")
@Schema(description = "店铺信息")
public class Shop extends BaseEntity {
    
    @Schema(description = "店铺名称")
    @TableField("name")
    private String name;
    
    @Schema(description = "店铺描述")
    @TableField("description")
    private String description;
    
    @Schema(description = "联系电话")
    @TableField("phone")
    private String phone;
    
    @Schema(description = "店铺地址")
    @TableField("address")
    private String address;
    
    @Schema(description = "营业时间")
    @TableField("business_hours")
    private String businessHours;
    
    @Schema(description = "Logo图片URL")
    @TableField("logo_url")
    private String logoUrl;
    
    @Schema(description = "营业状态 1:营业 0:休息")
    @TableField("status")
    private Integer status;
}
