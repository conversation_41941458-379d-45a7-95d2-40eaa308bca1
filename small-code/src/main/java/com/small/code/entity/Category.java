package com.small.code.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 菜品分类实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("small_categories")
@Schema(description = "菜品分类")
public class Category extends BaseEntity {
    
    @Schema(description = "分类名称")
    @TableField("name")
    private String name;
    
    @Schema(description = "分类图标URL")
    @TableField("icon_url")
    private String iconUrl;
    
    @Schema(description = "排序权重")
    @TableField("sort_order")
    private Integer sortOrder;
    
    @Schema(description = "状态 1:启用 0:禁用")
    @TableField("status")
    private Integer status;
}
