package com.small.code.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 用户信息实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("small_users")
@Schema(description = "用户信息")
public class User extends BaseEntity {
    
    @Schema(description = "微信OpenID")
    @TableField("openid")
    private String openid;
    
    @Schema(description = "微信UnionID")
    @TableField("unionid")
    private String unionid;
    
    @Schema(description = "昵称")
    @TableField("nickname")
    private String nickname;
    
    @Schema(description = "头像URL")
    @TableField("avatar_url")
    private String avatarUrl;
    
    @Schema(description = "手机号")
    @TableField("phone")
    private String phone;
    
    @Schema(description = "是否会员 0:非会员 1:会员")
    @TableField("is_member")
    private Integer isMember;
    
    @Schema(description = "会员到期日期")
    @TableField("member_expire_date")
    private LocalDate memberExpireDate;
    
    @Schema(description = "会员等级 0:普通 1:铜牌 2:银牌 3:金牌")
    @TableField("member_level")
    private Integer memberLevel;
    
    @Schema(description = "返现余额")
    @TableField("cashback_balance")
    private BigDecimal cashbackBalance;
    
    @Schema(description = "邀请码")
    @TableField("invite_code")
    private String inviteCode;
    
    @Schema(description = "邀请人ID")
    @TableField("inviter_id")
    private Long inviterId;
}
