package com.small.code.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 基础实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "基础实体")
public class BaseEntity {
    
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @Schema(description = "租户ID")
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    private Long tenantId;
    
    @Schema(description = "逻辑删除标识 1:有效 0:已删除")
    @TableLogic
    @TableField(value = "is_valid", fill = FieldFill.INSERT)
    private Integer isValid;
    
    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
