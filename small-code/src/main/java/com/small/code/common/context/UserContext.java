package com.small.code.common.context;

import lombok.Data;

/**
 * 用户上下文信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserContext {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 用户昵称
     */
    private String nickname;
    
    /**
     * 是否管理员
     */
    private Boolean isAdmin;
    
    /**
     * 请求IP
     */
    private String ipAddress;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 请求ID
     */
    private String requestId;
}
