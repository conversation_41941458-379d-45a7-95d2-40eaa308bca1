package com.small.code.common.context;

/**
 * 用户上下文持有者
 * 使用ThreadLocal存储当前请求的用户信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class UserContextHolder {
    
    private static final ThreadLocal<UserContext> CONTEXT_HOLDER = new ThreadLocal<>();
    
    /**
     * 设置用户上下文
     */
    public static void setContext(UserContext userContext) {
        CONTEXT_HOLDER.set(userContext);
    }
    
    /**
     * 获取用户上下文
     */
    public static UserContext getContext() {
        return CONTEXT_HOLDER.get();
    }
    
    /**
     * 获取当前用户ID
     */
    public static Long getCurrentUserId() {
        UserContext context = getContext();
        return context != null ? context.getUserId() : null;
    }
    
    /**
     * 获取当前租户ID
     */
    public static Long getCurrentTenantId() {
        UserContext context = getContext();
        return context != null ? context.getTenantId() : 1L; // 默认租户ID为1
    }
    
    /**
     * 获取当前请求ID
     */
    public static String getCurrentRequestId() {
        UserContext context = getContext();
        return context != null ? context.getRequestId() : null;
    }
    
    /**
     * 判断是否为管理员
     */
    public static boolean isAdmin() {
        UserContext context = getContext();
        return context != null && Boolean.TRUE.equals(context.getIsAdmin());
    }
    
    /**
     * 清除用户上下文
     */
    public static void clear() {
        CONTEXT_HOLDER.remove();
    }
}
