package com.small.code.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    // 成功
    SUCCESS(200, "操作成功"),
    
    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    UNPROCESSABLE_ENTITY(422, "业务逻辑错误"),
    
    // 服务器错误 5xx
    ERROR(500, "服务器内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    
    // 业务错误码 1xxx
    // 用户相关 10xx
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    INVALID_WECHAT_CODE(1003, "微信登录code无效"),
    TOKEN_EXPIRED(1004, "Token已过期"),
    TOKEN_INVALID(1005, "Token无效"),
    
    // 桌号相关 11xx
    TABLE_NOT_FOUND(1101, "桌号不存在"),
    TABLE_OCCUPIED(1102, "桌号已被占用"),
    TABLE_NUMBER_INVALID(1103, "桌号无效"),
    
    // 菜品相关 12xx
    DISH_NOT_FOUND(1201, "菜品不存在"),
    DISH_OUT_OF_STOCK(1202, "菜品库存不足"),
    CATEGORY_NOT_FOUND(1203, "菜品分类不存在"),
    
    // 订单相关 13xx
    ORDER_NOT_FOUND(1301, "订单不存在"),
    ORDER_STATUS_ERROR(1302, "订单状态错误"),
    ORDER_AMOUNT_ERROR(1303, "订单金额错误"),
    ORDER_ALREADY_PAID(1304, "订单已支付"),
    ORDER_CANNOT_CANCEL(1305, "订单无法取消"),
    
    // 支付相关 14xx
    PAYMENT_FAILED(1401, "支付失败"),
    PAYMENT_TIMEOUT(1402, "支付超时"),
    REFUND_FAILED(1403, "退款失败"),
    WECHAT_PAY_ERROR(1404, "微信支付错误"),
    
    // 会员相关 15xx
    MEMBER_NOT_FOUND(1501, "会员不存在"),
    COUPON_NOT_FOUND(1502, "优惠券不存在"),
    COUPON_EXPIRED(1503, "优惠券已过期"),
    COUPON_USED(1504, "优惠券已使用"),
    INVITE_CODE_INVALID(1505, "邀请码无效"),
    
    // 店铺相关 16xx
    SHOP_NOT_FOUND(1601, "店铺不存在"),
    SHOP_CLOSED(1602, "店铺已打烊"),
    
    // 系统相关 19xx
    TENANT_NOT_FOUND(1901, "租户不存在"),
    CONFIG_NOT_FOUND(1902, "配置不存在"),
    FILE_UPLOAD_ERROR(1903, "文件上传失败");
    
    private final Integer code;
    private final String message;
}
