package com.small.code.common.constant;

/**
 * 通用常量
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class CommonConstant {
    
    // 逻辑删除
    public static final Integer VALID = 1;
    public static final Integer INVALID = 0;
    
    // 状态
    public static final Integer ENABLED = 1;
    public static final Integer DISABLED = 0;
    
    // 是否推荐
    public static final Integer RECOMMENDED = 1;
    public static final Integer NOT_RECOMMENDED = 0;
    
    // 是否会员
    public static final Integer IS_MEMBER = 1;
    public static final Integer NOT_MEMBER = 0;
    
    // 桌号状态
    public static final Integer TABLE_FREE = 0;      // 空闲
    public static final Integer TABLE_OCCUPIED = 1;  // 占用
    public static final Integer TABLE_CLEANING = 2;  // 清理中
    
    // 订单状态
    public static final String ORDER_UNPAID = "unpaid";       // 待支付
    public static final String ORDER_PAID = "paid";           // 已支付
    public static final String ORDER_COOKING = "cooking";     // 制作中
    public static final String ORDER_COMPLETED = "completed"; // 已完成
    public static final String ORDER_CANCELLED = "cancelled"; // 已取消
    
    // 支付状态
    public static final Integer PAYMENT_UNPAID = 0;  // 未支付
    public static final Integer PAYMENT_PAID = 1;    // 已支付
    public static final Integer PAYMENT_FAILED = 2;  // 支付失败
    
    // 支付方式
    public static final String PAYMENT_WECHAT = "wechat";
    
    // 商品类型
    public static final Integer ITEM_TYPE_DISH = 1;     // 单品
    public static final Integer ITEM_TYPE_MEAL_SET = 2; // 套餐
    
    // 优惠券类型
    public static final Integer COUPON_TYPE_DISCOUNT = 1;  // 满减券
    public static final Integer COUPON_TYPE_RATE = 2;      // 折扣券
    public static final Integer COUPON_TYPE_NEW_USER = 3;  // 新用户券
    
    // 优惠券状态
    public static final Integer COUPON_UNUSED = 1;   // 未使用
    public static final Integer COUPON_USED = 2;     // 已使用
    public static final Integer COUPON_EXPIRED = 3;  // 已过期
    
    // 会员等级
    public static final Integer MEMBER_LEVEL_NORMAL = 0;  // 普通
    public static final Integer MEMBER_LEVEL_BRONZE = 1;  // 铜牌
    public static final Integer MEMBER_LEVEL_SILVER = 2;  // 银牌
    public static final Integer MEMBER_LEVEL_GOLD = 3;    // 金牌
    
    // 公告类型
    public static final Integer ANNOUNCEMENT_SYSTEM = 1;     // 系统公告
    public static final Integer ANNOUNCEMENT_ACTIVITY = 2;   // 活动公告
    public static final Integer ANNOUNCEMENT_MAINTENANCE = 3; // 维护公告
    
    // 公告优先级
    public static final Integer PRIORITY_LOW = 1;    // 低
    public static final Integer PRIORITY_MEDIUM = 2; // 中
    public static final Integer PRIORITY_HIGH = 3;   // 高
    
    // 默认值
    public static final Integer DEFAULT_PAGE_SIZE = 20;
    public static final Integer DEFAULT_PAGE_NUM = 1;
    public static final Integer DEFAULT_SORT_ORDER = 0;
    public static final Integer UNLIMITED_STOCK = -1;
    
    // 桌号范围
    public static final Integer MIN_TABLE_NUMBER = 1;
    public static final Integer MAX_TABLE_NUMBER = 12;
    
    // JWT相关
    public static final String JWT_HEADER = "Authorization";
    public static final String JWT_PREFIX = "Bearer ";
    public static final String JWT_CLAIM_USER_ID = "userId";
    public static final String JWT_CLAIM_TENANT_ID = "tenantId";
    
    // 微信相关
    public static final String WECHAT_LOGIN_URL = "https://api.weixin.qq.com/sns/jscode2session";
    
    // 文件相关
    public static final String[] ALLOWED_IMAGE_TYPES = {"jpg", "jpeg", "png", "gif", "webp"};
    public static final Long MAX_FILE_SIZE = 10 * 1024 * 1024L; // 10MB
}
