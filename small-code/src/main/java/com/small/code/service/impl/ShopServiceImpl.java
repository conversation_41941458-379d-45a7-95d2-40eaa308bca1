package com.small.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.small.code.common.exception.BusinessException;
import com.small.code.common.result.ResultCode;
import com.small.code.entity.Shop;
import com.small.code.mapper.ShopMapper;
import com.small.code.service.ShopService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 店铺信息服务实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class ShopServiceImpl extends ServiceImpl<ShopMapper, Shop> implements ShopService {
    
    @Override
    public Shop getShopInfo() {
        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(Shop::getCreatedAt);
        queryWrapper.last("LIMIT 1");
        
        Shop shop = this.getOne(queryWrapper);
        if (shop == null) {
            throw new BusinessException(ResultCode.SHOP_NOT_FOUND);
        }
        
        return shop;
    }
}
