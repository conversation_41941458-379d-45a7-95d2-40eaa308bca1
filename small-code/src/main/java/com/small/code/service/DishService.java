package com.small.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.small.code.entity.Category;
import com.small.code.entity.Dish;

import java.util.List;

/**
 * 菜品信息服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface DishService extends IService<Dish> {
    
    /**
     * 获取菜品分类列表
     */
    List<Category> getCategories();
    
    /**
     * 获取所有菜品列表
     */
    List<Dish> getAllDishes();
    
    /**
     * 获取推荐菜品列表
     */
    List<Dish> getRecommendedDishes();
}
