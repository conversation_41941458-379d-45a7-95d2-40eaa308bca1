package com.small.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.small.code.common.constant.CommonConstant;
import com.small.code.entity.Category;
import com.small.code.entity.Dish;
import com.small.code.mapper.CategoryMapper;
import com.small.code.mapper.DishMapper;
import com.small.code.service.DishService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 菜品信息服务实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DishServiceImpl extends ServiceImpl<DishMapper, Dish> implements DishService {
    
    private final CategoryMapper categoryMapper;
    
    @Override
    public List<Category> getCategories() {
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Category::getStatus, CommonConstant.ENABLED);
        queryWrapper.orderByAsc(Category::getSortOrder);
        queryWrapper.orderByAsc(Category::getId);
        
        return categoryMapper.selectList(queryWrapper);
    }
    
    @Override
    public List<Dish> getAllDishes() {
        LambdaQueryWrapper<Dish> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Dish::getStatus, CommonConstant.ENABLED);
        queryWrapper.orderByAsc(Dish::getSortOrder);
        queryWrapper.orderByAsc(Dish::getId);
        
        return this.list(queryWrapper);
    }
    
    @Override
    public List<Dish> getRecommendedDishes() {
        LambdaQueryWrapper<Dish> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Dish::getStatus, CommonConstant.ENABLED);
        queryWrapper.eq(Dish::getIsRecommended, CommonConstant.RECOMMENDED);
        queryWrapper.orderByAsc(Dish::getSortOrder);
        queryWrapper.orderByAsc(Dish::getId);
        
        return this.list(queryWrapper);
    }
}
