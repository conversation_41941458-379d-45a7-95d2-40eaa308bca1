package com.small.code.controller;

import com.small.code.common.result.Result;
import com.small.code.entity.Category;
import com.small.code.entity.Dish;
import com.small.code.service.DishService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 菜品管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/dishes")
@RequiredArgsConstructor
@Tag(name = "菜品管理", description = "菜品相关API")
public class DishController {
    
    private final DishService dishService;
    
    @Operation(summary = "获取菜品分类", description = "获取菜品分类列表")
    @GetMapping("/categories")
    public Result<List<Category>> getCategories() {
        List<Category> categories = dishService.getCategories();
        return Result.success(categories);
    }
    
    @Operation(summary = "获取所有菜品", description = "获取所有可用菜品列表")
    @GetMapping("/list")
    public Result<List<Dish>> getAllDishes() {
        List<Dish> dishes = dishService.getAllDishes();
        return Result.success(dishes);
    }
    
    @Operation(summary = "获取推荐菜品", description = "获取推荐菜品列表")
    @GetMapping("/recommended")
    public Result<List<Dish>> getRecommendedDishes() {
        List<Dish> dishes = dishService.getRecommendedDishes();
        return Result.success(dishes);
    }
}
