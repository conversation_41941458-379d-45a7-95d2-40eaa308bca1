package com.small.code.controller;

import com.small.code.common.result.Result;
import com.small.code.entity.Dish;
import com.small.code.entity.Shop;
import com.small.code.service.DishService;
import com.small.code.service.ShopService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 店铺信息控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/shop")
@RequiredArgsConstructor
@Tag(name = "店铺管理", description = "店铺信息相关API")
public class ShopController {
    
    private final ShopService shopService;
    private final DishService dishService;
    
    @Operation(summary = "获取店铺信息", description = "获取店铺基本信息")
    @GetMapping("/info")
    public Result<Shop> getShopInfo() {
        Shop shop = shopService.getShopInfo();
        return Result.success(shop);
    }
    
    @Operation(summary = "获取推荐菜品", description = "获取首页推荐菜品列表")
    @GetMapping("/recommended-dishes")
    public Result<List<Dish>> getRecommendedDishes() {
        List<Dish> dishes = dishService.getRecommendedDishes();
        return Result.success(dishes);
    }
}
