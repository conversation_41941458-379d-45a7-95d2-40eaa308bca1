# IDE相关文件
.idea/
.vscode/
*.swp
*.swo
*~

# 操作系统相关文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# 微信小程序相关
# 私有配置文件（包含个人开发者信息）
small/project.private.config.json
small/project.config.json

# 微信开发者工具生成的文件
small/miniprogram_npm/
small/.tea/

# 编译输出
small/dist/
small/build/


# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.cache/

# 环境配置文件（可能包含敏感信息）
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 后端相关 - Spring Boot & Maven
# Java编译文件
*.class
*.jar
*.war
*.ear

# Maven
target/
.mvn/
mvnw
mvnw.cmd
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Spring Boot
*.original
spring-boot-*.jar

# 应用配置文件（包含敏感信息）
application-*.yml
application-*.yaml
application-*.properties
!application.yml.template
!application.properties.template

# 日志文件
logs/
*.log
*.log.*
log/

# 数据库相关
*.db
*.sqlite
*.sqlite3
*.h2.db

# IDE相关
.idea/
.vscode/
*.iml
*.ipr
*.iws
.project
.classpath
.settings/
.metadata/
bin/
out/
build/

# 热部署
.spring-boot-devtools.properties

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Supabase 配置文件（包含敏感信息）
supabase/
.env.supabase
supabase-config.json
supabase-secrets.json

# 微信支付配置文件（包含敏感信息）
wechat-pay-*.pem
wechat-pay-config.json
wechat-secrets.json

# JWT密钥文件
jwt-*.key
jwt-secrets.json

# 配置文件备份
*.bak
*.backup

# 测试覆盖率报告
coverage/
.nyc_output/

# 其他
*.zip
*.tar.gz
*.rar


small/docs/
.cunzhi-memory