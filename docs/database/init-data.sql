-- 川味小厨餐饮小程序 - 初始化数据
-- 创建时间: 2024-12-05
-- 版本: v1.0.0

-- 1. 插入店铺信息
INSERT INTO small_shops (tenant_id, name, description, phone, address, business_hours, logo_url, status) VALUES
(1, '川味小厨', '正宗川菜 · 地道美味', '023-8888-6666', '重庆市渝中区美食街88号', '09:00 - 22:00', '/images/logo.png', 1);

-- 2. 插入桌号信息 (1-12号桌)
INSERT INTO small_tables (tenant_id, table_number, seats, status) VALUES
(1, 1, 4, 0),
(1, 2, 4, 0),
(1, 3, 4, 0),
(1, 4, 6, 0),
(1, 5, 6, 0),
(1, 6, 4, 0),
(1, 7, 4, 0),
(1, 8, 8, 0),
(1, 9, 4, 0),
(1, 10, 4, 0),
(1, 11, 6, 0),
(1, 12, 8, 0);

-- 3. 插入菜品分类
INSERT INTO small_categories (tenant_id, name, icon_url, sort_order, status) VALUES
(1, '热菜', '/images/categories/hot.png', 1, 1),
(1, '凉菜', '/images/categories/cold.png', 2, 1),
(1, '汤品', '/images/categories/soup.png', 3, 1),
(1, '川菜', '/images/categories/sichuan.png', 4, 1),
(1, '素食', '/images/categories/vegetarian.png', 5, 1),
(1, '海鲜', '/images/categories/seafood.png', 6, 1),
(1, '烧烤', '/images/categories/bbq.png', 7, 1),
(1, '主食', '/images/categories/staple.png', 8, 1),
(1, '饮品', '/images/categories/drinks.png', 9, 1),
(1, '甜品', '/images/categories/dessert.png', 10, 1);

-- 4. 插入菜品信息
INSERT INTO small_dishes (tenant_id, category_id, name, description, price, emoji, image_url, stock, is_recommended, status, sort_order) VALUES
-- 热菜
(1, 1, '宫保鸡丁', '经典川菜，鸡肉嫩滑，花生香脆，酸甜微辣', 28.00, '🍗', '/images/dishes/gongbao.jpg', -1, 1, 1, 1),
(1, 1, '麻婆豆腐', '嫩滑豆腐配麻辣肉末，口感丰富层次分明', 18.00, '🥘', '/images/dishes/mapo.jpg', -1, 0, 1, 2),
(1, 1, '回锅肉', '四川传统名菜，肥而不腻，香辣下饭', 32.00, '🥓', '/images/dishes/huiguo.jpg', -1, 1, 1, 3),
(1, 1, '水煮鱼', '鲜嫩鱼片，麻辣鲜香，川菜经典', 45.00, '🐟', '/images/dishes/shuizhuyu.jpg', -1, 1, 1, 4),

-- 凉菜
(1, 2, '夫妻肺片', '川菜经典凉菜，麻辣鲜香', 25.00, '🥩', '/images/dishes/fuqi.jpg', -1, 0, 1, 1),
(1, 2, '口水鸡', '麻辣鲜香，口感嫩滑', 22.00, '🐔', '/images/dishes/koushuiji.jpg', -1, 1, 1, 2),
(1, 2, '蒜泥白肉', '清爽不腻，蒜香浓郁', 20.00, '🥩', '/images/dishes/suanni.jpg', -1, 0, 1, 3),

-- 汤品
(1, 3, '酸辣汤', '酸辣开胃，暖胃佳品', 12.00, '🍲', '/images/dishes/suanla.jpg', -1, 0, 1, 1),
(1, 3, '冬瓜排骨汤', '清淡营养，老少皆宜', 18.00, '🍲', '/images/dishes/donggua.jpg', -1, 1, 1, 2),
(1, 3, '番茄鸡蛋汤', '酸甜可口，营养丰富', 10.00, '🍅', '/images/dishes/fanqie.jpg', -1, 0, 1, 3),

-- 川菜
(1, 4, '鱼香肉丝', '川菜经典，酸甜咸辣', 26.00, '🥢', '/images/dishes/yuxiang.jpg', -1, 1, 1, 1),
(1, 4, '辣子鸡', '麻辣香脆，川菜代表', 35.00, '🌶️', '/images/dishes/laziji.jpg', -1, 0, 1, 2),
(1, 4, '毛血旺', '麻辣鲜香，重庆特色', 38.00, '🌶️', '/images/dishes/maoxuewang.jpg', -1, 1, 1, 3),

-- 素食
(1, 5, '地三鲜', '茄子土豆青椒，家常美味', 16.00, '🍆', '/images/dishes/disanxian.jpg', -1, 0, 1, 1),
(1, 5, '干煸豆角', '香辣下饭，素食经典', 14.00, '🫘', '/images/dishes/ganbian.jpg', -1, 0, 1, 2),
(1, 5, '清炒时蔬', '新鲜蔬菜，清淡健康', 12.00, '🥬', '/images/dishes/shishu.jpg', -1, 0, 1, 3),

-- 主食
(1, 8, '米饭', '优质大米，粒粒饱满', 3.00, '🍚', '/images/dishes/rice.jpg', -1, 0, 1, 1),
(1, 8, '蛋炒饭', '香喷喷的蛋炒饭', 8.00, '🍳', '/images/dishes/chaofan.jpg', -1, 0, 1, 2),
(1, 8, '担担面', '四川特色面条，麻辣鲜香', 15.00, '🍜', '/images/dishes/dandan.jpg', -1, 1, 1, 3),

-- 饮品
(1, 9, '柠檬蜂蜜茶', '清香甘甜，生津止渴', 8.00, '🍋', '/images/dishes/lemon.jpg', -1, 0, 1, 1),
(1, 9, '酸梅汤', '传统饮品，酸甜解腻', 6.00, '🥤', '/images/dishes/suanmei.jpg', -1, 1, 1, 2),
(1, 9, '鲜榨橙汁', '新鲜橙子现榨，维C丰富', 12.00, '🍊', '/images/dishes/orange.jpg', -1, 0, 1, 3);

-- 5. 插入系统配置
INSERT INTO system_configs (tenant_id, config_key, config_value, description) VALUES
(1, 'shop.business_status', '1', '店铺营业状态 1:营业 0:休息'),
(1, 'order.auto_cancel_minutes', '30', '订单自动取消时间(分钟)'),
(1, 'payment.timeout_minutes', '15', '支付超时时间(分钟)'),
(1, 'member.annual_fee', '99', '会员年费(元)'),
(1, 'invite.reward_amount', '50', '邀请奖励金额(元)');

-- 6. 插入公告信息
INSERT INTO announcements (tenant_id, title, content, type, priority, status, start_time, end_time) VALUES
(1, '欢迎光临川味小厨', '正宗川菜，地道美味，欢迎品尝！', 1, 2, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days'),
(1, '新用户优惠', '新用户注册即送10元优惠券，快来体验吧！', 2, 3, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '7 days');
