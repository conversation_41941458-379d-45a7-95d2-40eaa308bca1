# Supabase 配置指南

## 📋 配置步骤

### 1. 创建 Supabase 项目
1. 访问 [Supabase](https://supabase.com)
2. 创建新项目
3. 记录项目信息：
   - Project URL: `https://YOUR_PROJECT_ID.supabase.co`
   - API Keys (anon key 和 service_role key)
   - Database Password

### 2. 执行数据库脚本
1. 进入 Supabase Dashboard
2. 点击 "SQL Editor"
3. 执行以下脚本（按顺序）：
   - `docs/database/supabase-schema.sql` - 创建表结构
   - `docs/database/init-data.sql` - 插入初始数据

### 3. 配置应用
复制 `application.yml.template` 为 `application-dev.yml`，并填入真实配置：

```yaml
spring:
  datasource:
    url: **************************************************************
    username: postgres
    password: YOUR_SUPABASE_DB_PASSWORD

app:
  supabase:
    url: https://YOUR_PROJECT_ID.supabase.co
    anon-key: YOUR_SUPABASE_ANON_KEY
    service-role-key: YOUR_SUPABASE_SERVICE_ROLE_KEY
```

### 4. 微信配置
在微信开放平台获取以下信息：

```yaml
app:
  wechat:
    app-id: YOUR_WECHAT_MINI_PROGRAM_APP_ID
    app-secret: YOUR_WECHAT_MINI_PROGRAM_APP_SECRET
    
  wechat-pay:
    app-id: YOUR_WECHAT_PAY_APP_ID
    mch-id: YOUR_WECHAT_PAY_MERCHANT_ID
    private-key-path: classpath:wechat-pay-private-key.pem
    merchant-serial-number: YOUR_WECHAT_PAY_SERIAL_NUMBER
    api-v3-key: YOUR_WECHAT_PAY_API_V3_KEY
```

### 5. JWT 配置
生成一个至少32位的随机字符串作为JWT密钥：

```yaml
app:
  jwt:
    secret: YOUR_JWT_SECRET_KEY_AT_LEAST_32_CHARACTERS_LONG
```

## 🔐 安全注意事项

1. **不要提交敏感配置到Git**
   - 使用 `application-dev.yml` 或 `application-prod.yml`
   - 这些文件已在 `.gitignore` 中排除

2. **生产环境使用环境变量**
   ```bash
   export SUPABASE_DB_PASSWORD=your_password
   export JWT_SECRET=your_jwt_secret
   export WECHAT_APP_SECRET=your_app_secret
   ```

3. **微信支付私钥文件**
   - 将 `wechat-pay-private-key.pem` 放在 `src/main/resources/` 目录
   - 该文件已在 `.gitignore` 中排除

## 🚀 启动应用

1. 确保配置文件正确
2. 运行应用：
   ```bash
   cd small-code
   mvn spring-boot:run -Dspring-boot.run.profiles=dev
   ```

3. 访问 API 文档：
   - Swagger UI: http://localhost:8080/api/v1/swagger-ui.html
   - API Docs: http://localhost:8080/api/v1/api-docs

## 📊 数据库连接测试

启动应用后，检查日志确认数据库连接成功：
```
2024-12-05 10:00:00 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-12-05 10:00:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
```

## 🔧 常见问题

### 1. 数据库连接失败
- 检查 Supabase 项目是否正常运行
- 确认数据库密码正确
- 检查网络连接

### 2. 表不存在错误
- 确认已执行 `supabase-schema.sql` 脚本
- 检查表名是否正确（注意大小写）

### 3. JWT 错误
- 确认 JWT 密钥长度至少32位
- 检查密钥是否包含特殊字符需要转义
