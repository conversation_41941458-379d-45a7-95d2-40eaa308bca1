# 项目结构说明

## 📁 目录结构

```
small-code/
├── src/main/java/com/small/code/
│   ├── SmallCodeApplication.java          # 应用启动类
│   ├── common/                            # 通用组件
│   │   ├── constant/                      # 常量定义
│   │   │   └── CommonConstant.java        # 通用常量
│   │   ├── exception/                     # 异常处理
│   │   │   ├── BusinessException.java     # 业务异常
│   │   │   └── GlobalExceptionHandler.java # 全局异常处理器
│   │   └── result/                        # 响应结果
│   │       ├── Result.java                # 统一响应结果
│   │       └── ResultCode.java            # 响应状态码
│   ├── config/                            # 配置类
│   │   ├── MyBatisPlusConfig.java         # MyBatis Plus配置
│   │   ├── MyMetaObjectHandler.java       # 字段自动填充
│   │   └── TenantLineHandler.java         # 多租户处理
│   ├── controller/                        # 控制器层
│   │   ├── DishController.java            # 菜品管理
│   │   └── ShopController.java            # 店铺管理
│   ├── entity/                            # 实体类
│   │   ├── BaseEntity.java                # 基础实体
│   │   ├── Category.java                  # 菜品分类
│   │   ├── Dish.java                      # 菜品信息
│   │   ├── Order.java                     # 订单主表
│   │   ├── OrderItem.java                 # 订单详情
│   │   ├── Shop.java                      # 店铺信息
│   │   ├── Table.java                     # 桌号管理
│   │   └── User.java                      # 用户信息
│   ├── mapper/                            # 数据访问层
│   │   ├── CategoryMapper.java            # 菜品分类Mapper
│   │   ├── DishMapper.java                # 菜品信息Mapper
│   │   ├── OrderItemMapper.java           # 订单详情Mapper
│   │   ├── OrderMapper.java               # 订单主表Mapper
│   │   ├── ShopMapper.java                # 店铺信息Mapper
│   │   ├── TableMapper.java               # 桌号管理Mapper
│   │   └── UserMapper.java                # 用户信息Mapper
│   └── service/                           # 服务层
│       ├── DishService.java               # 菜品服务接口
│       ├── ShopService.java               # 店铺服务接口
│       └── impl/                          # 服务实现
│           ├── DishServiceImpl.java       # 菜品服务实现
│           └── ShopServiceImpl.java       # 店铺服务实现
├── src/main/resources/
│   ├── application.yml                    # 主配置文件
│   └── application.yml.template           # 配置模板
└── pom.xml                                # Maven依赖配置
```

## 🏗️ 架构设计

### 分层架构
- **Controller层**: 处理HTTP请求，参数校验，调用Service
- **Service层**: 业务逻辑处理，事务管理
- **Mapper层**: 数据访问，SQL操作
- **Entity层**: 数据库实体映射

### 核心特性
- **多租户支持**: 所有业务表支持租户隔离
- **逻辑删除**: 使用 `is_valid` 字段实现软删除
- **自动填充**: 创建时间、更新时间、租户ID自动填充
- **统一响应**: 使用 `Result<T>` 统一API响应格式
- **异常处理**: 全局异常处理器统一处理异常
- **API文档**: 集成SpringDoc自动生成API文档

## 🔧 技术栈

### 后端框架
- **Spring Boot 3.5.4**: 主框架
- **MyBatis Plus 3.5.5**: ORM框架
- **PostgreSQL**: 数据库（Supabase）

### 工具库
- **Lombok**: 简化Java代码
- **Hutool 5.8.25**: Java工具包
- **Guava 32.1.3**: Google工具库
- **FastJSON 2.0.45**: JSON处理

### 文档和测试
- **SpringDoc 2.3.0**: API文档生成
- **JWT 0.12.3**: 身份认证
- **微信支付SDK 0.2.12**: 支付集成

## 📝 开发规范

### 命名规范
- **包名**: 小写，使用点分隔
- **类名**: 大驼峰命名法
- **方法名**: 小驼峰命名法
- **常量**: 全大写，下划线分隔
- **数据库表**: 小写，下划线分隔，统一前缀 `small_`

### 代码规范
- 所有类必须添加类注释
- 公共方法必须添加方法注释
- 使用 `@Schema` 注解描述API字段
- 异常处理使用统一的 `BusinessException`
- 日志使用 `@Slf4j` 注解

### 数据库规范
- 主键统一使用 `id`，类型 `BIGSERIAL`
- 所有业务表包含 `tenant_id` 字段
- 使用 `is_valid` 字段实现逻辑删除
- 时间字段使用 `created_at` 和 `updated_at`
- 外键约束明确定义

## 🚀 快速开始

### 1. 环境准备
- JDK 17+
- Maven 3.6+
- PostgreSQL (Supabase)

### 2. 配置数据库
参考 `docs/SUPABASE-CONFIG.md` 配置Supabase数据库

### 3. 启动应用
```bash
cd small-code
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 4. 访问API文档
- Swagger UI: http://localhost:8080/api/v1/swagger-ui.html
- API JSON: http://localhost:8080/api/v1/api-docs

## 📋 待实现功能

### 第一阶段（当前）
- [x] 项目架构搭建
- [x] 基础实体和Mapper
- [x] 店铺和菜品API
- [ ] 用户认证模块
- [ ] 桌号管理模块
- [ ] 订单管理模块

### 第二阶段
- [ ] 微信支付集成
- [ ] 会员系统（简化版）
- [ ] 优惠券基础功能
- [ ] 文件上传功能

### 第三阶段
- [ ] 套餐功能
- [ ] 复杂会员等级
- [ ] 返现机制
- [ ] 数据统计分析

## 🔍 API接口

当前已实现的API接口：

### 店铺管理
- `GET /shop/info` - 获取店铺信息
- `GET /shop/recommended-dishes` - 获取推荐菜品

### 菜品管理
- `GET /dishes/categories` - 获取菜品分类
- `GET /dishes/list` - 获取所有菜品
- `GET /dishes/recommended` - 获取推荐菜品

更多API接口正在开发中，详见 `docs/API-DESIGN.md`
