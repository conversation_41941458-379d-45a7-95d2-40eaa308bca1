# 川味小厨餐饮小程序 - 后端接口设计文档 (简化版)

## 🚀 **后端开发关键要点**

### **核心设计原则**
1. **简化优先**: 当前版本专注核心点餐功能，复杂功能预留数据库字段但暂不实现
2. **前端适配**: API接口完全匹配小程序前端数据结构，包含emoji字段和数字桌号
3. **多租户架构**: 所有表包含tenant_id，支持多商户部署
4. **MyBatis Plus**: 使用is_valid逻辑删除，自动填充时间字段

### **技术栈要求**
- **后端**: Java 17 + Spring Boot 3.x + MyBatis Plus
- **数据库**: Supabase (PostgreSQL)
- **认证**: 微信小程序登录 + JWT
- **支付**: 微信支付 API v3

### **开发优先级**
1. **第一阶段**: 用户认证、桌号管理、菜品展示、订单创建、微信支付
2. **第二阶段**: 简化会员系统、基础优惠券功能
3. **第三阶段**: 套餐功能、复杂会员等级、返现机制

### **关键数据结构匹配**
- **桌号**: 数字类型(1-12)，非字符串
- **菜品**: 必须包含emoji字段，图片使用相对路径
- **订单状态**: 统一管理，unpaid/paid/cooking/completed/cancelled
- **会员**: 简化为is_member布尔值，非复杂等级

### **MyBatis Plus配置要点**
```yaml
# 关键配置示例
mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: isValid
      logic-delete-value: 0
      logic-not-delete-value: 1
  configuration:
    map-underscore-to-camel-case: true
```

---

## 项目背景

### 项目概述
川味小厨是一款专注于堂食点餐的微信小程序，为川菜餐厅提供数字化点餐解决方案。通过扫码或手动输入桌号的方式，顾客可以便捷地浏览菜单、下单点餐，享受优质的用餐体验。

### 目标用户
- **主要用户**: 到店用餐的顾客
- **使用场景**: 堂食点餐、会员服务、订单管理、微信支付
- **核心需求**: 快速点餐、会员优惠、订单跟踪

### 设计理念
- **简约高效**: 3页面设计，减少操作步骤
- **川菜特色**: 深绿+金色主题，体现川菜文化
- **会员导向**: 简化的会员体系(是会员/非会员)
- **数据驱动**: 支持后端数据管理和实时更新

### 开发策略
- **当前版本**: 专注核心功能，简化开发复杂度
- **数据库设计**: 保持完整性，适配后续业务升级改造
- **API接口**: 当前简化实现，预留扩展能力

## 技术架构

### 前端技术栈
- **框架**: 微信小程序原生开发
- **语言**: TypeScript + WXML + WXSS
- **状态管理**: 本地存储 + 页面数据绑定
- **UI设计**: 响应式布局，适配多种屏幕
- **支付**: 微信支付 JSAPI

### 后端技术栈
- **语言**: Java 17
- **框架**: Spring Boot 3.x
- **数据库**: Supabase (PostgreSQL)
- **认证**: 微信小程序登录 + JWT
- **文件存储**: Supabase Storage
- **支付**: 微信支付 API v3

## 核心功能模块 (当前版本)

### 1. 桌号管理
- 数字桌号选择 (1-12号桌)
- 桌号状态管理 (空闲/占用)
- 简化桌号验证逻辑

### 2. 菜品管理
- 分类浏览 (热菜、凉菜、汤品、饮品、主食等10个分类)
- 菜品详情展示 (价格、描述、图片、emoji、推荐标识)
- 库存状态管理
- **暂不实现**: 套餐管理 (预留数据库设计)

### 3. 购物车与订单
- 购物车商品管理 (单品)
- 订单生成与微信支付
- 订单状态统一管理
- 历史订单查询

### 4. 会员系统 (简化版)
- 会员状态管理 (是会员/非会员)
- 优惠券基础功能
- **暂不实现**: 复杂等级体系、返现机制 (预留数据库设计)

### 5. 店铺管理
- 店铺基本信息
- 营业时间设置
- 推荐菜品设置

### 6. 支付系统
- 微信支付集成
- 支付回调处理
- 统一的订单和支付状态管理

## 数据库设计 (MySQL语法，部署时转换为PostgreSQL)

### 设计原则
- **多租户支持**: 所有业务表增加 `tenant_id` 字段，支持多租户架构
- **逻辑删除**: 所有表增加 `is_valid` 字段，适配MyBatis Plus逻辑删除
- **索引优化**: 为租户ID和逻辑删除字段建立索引，提升查询性能
- **唯一约束**: 关键字段在租户维度保证唯一性

### MyBatis Plus适配说明
- `tenant_id`: 租户隔离，配置MyBatis Plus多租户插件
- `is_valid`: 逻辑删除标识，1=有效，0=已删除
- `created_at/updated_at`: 自动填充时间字段
- 索引策略: 优先查询条件为租户ID + 业务字段

### 核心业务表 (带small_前缀)

#### 1. 店铺信息表 (small_shops)
```sql
CREATE TABLE small_shops (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  name VARCHAR(100) NOT NULL COMMENT '店铺名称',
  description VARCHAR(200) COMMENT '店铺描述',
  phone VARCHAR(20) COMMENT '联系电话',
  address VARCHAR(200) COMMENT '店铺地址',
  business_hours VARCHAR(50) COMMENT '营业时间',
  logo_url VARCHAR(200) COMMENT 'Logo图片URL',
  status TINYINT DEFAULT 1 COMMENT '营业状态 1:营业 0:休息',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_is_valid (is_valid)
);
```

#### 2. 桌号管理表 (small_tables)
```sql
CREATE TABLE small_tables (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  table_number INT NOT NULL COMMENT '桌号(数字1-12)',
  seats INT DEFAULT 4 COMMENT '座位数',
  status TINYINT DEFAULT 0 COMMENT '状态 0:空闲 1:占用 2:清理中',
  qr_code VARCHAR(200) COMMENT '二维码内容',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tenant_table (tenant_id, table_number),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_is_valid (is_valid)
);
```

#### 3. 菜品分类表 (small_categories)
```sql
CREATE TABLE small_categories (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  name VARCHAR(50) NOT NULL COMMENT '分类名称',
  icon_url VARCHAR(200) COMMENT '分类图标URL',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  status TINYINT DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_is_valid (is_valid)
);
```

#### 4. 菜品信息表 (small_dishes)
```sql
CREATE TABLE small_dishes (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  category_id BIGINT NOT NULL COMMENT '分类ID',
  name VARCHAR(100) NOT NULL COMMENT '菜品名称',
  description TEXT COMMENT '菜品描述',
  price DECIMAL(10,2) NOT NULL COMMENT '价格',
  emoji VARCHAR(10) COMMENT '菜品emoji表情',
  image_url VARCHAR(200) COMMENT '菜品图片URL',
  stock INT DEFAULT -1 COMMENT '库存数量 -1:无限制',
  is_recommended TINYINT DEFAULT 0 COMMENT '是否推荐 1:是 0:否',
  status TINYINT DEFAULT 1 COMMENT '状态 1:可售 0:下架',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_category_id (category_id),
  INDEX idx_is_valid (is_valid),
  FOREIGN KEY (category_id) REFERENCES small_categories(id)
);
```

#### 5. 套餐信息表 (small_meal_sets) - 预留扩展
```sql
CREATE TABLE small_meal_sets (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  name VARCHAR(100) NOT NULL COMMENT '套餐名称',
  description TEXT COMMENT '套餐描述',
  original_price DECIMAL(10,2) NOT NULL COMMENT '原价',
  set_price DECIMAL(10,2) NOT NULL COMMENT '套餐价',
  image_url VARCHAR(200) COMMENT '套餐图片URL',
  is_recommended TINYINT DEFAULT 0 COMMENT '是否推荐 1:是 0:否',
  status TINYINT DEFAULT 1 COMMENT '状态 1:可售 0:下架',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_is_valid (is_valid)
);
```

#### 6. 套餐菜品关联表 (small_meal_set_dishes) - 预留扩展
```sql
CREATE TABLE small_meal_set_dishes (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  meal_set_id BIGINT NOT NULL COMMENT '套餐ID',
  dish_id BIGINT NOT NULL COMMENT '菜品ID',
  quantity INT DEFAULT 1 COMMENT '数量',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_is_valid (is_valid),
  FOREIGN KEY (meal_set_id) REFERENCES small_meal_sets(id),
  FOREIGN KEY (dish_id) REFERENCES small_dishes(id)
);
```

#### 7. 用户信息表 (small_users)
```sql
CREATE TABLE small_users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  openid VARCHAR(100) NOT NULL COMMENT '微信OpenID',
  unionid VARCHAR(100) COMMENT '微信UnionID',
  nickname VARCHAR(100) COMMENT '昵称',
  avatar_url VARCHAR(200) COMMENT '头像URL',
  phone VARCHAR(20) COMMENT '手机号',
  is_member TINYINT DEFAULT 0 COMMENT '是否会员 0:非会员 1:会员',
  member_expire_date DATE COMMENT '会员到期日期',
  -- 预留字段，适配后续升级
  member_level TINYINT DEFAULT 0 COMMENT '会员等级 0:普通 1:铜牌 2:银牌 3:金牌',
  cashback_balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '返现余额',
  invite_code VARCHAR(20) COMMENT '邀请码',
  inviter_id BIGINT COMMENT '邀请人ID',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tenant_openid (tenant_id, openid),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_invite_code (invite_code),
  INDEX idx_inviter_id (inviter_id),
  INDEX idx_is_valid (is_valid),
  FOREIGN KEY (inviter_id) REFERENCES small_users(id)
);
```

#### 8. 订单主表 (small_orders)
```sql
CREATE TABLE small_orders (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  order_number VARCHAR(50) NOT NULL COMMENT '订单号',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  table_number INT NOT NULL COMMENT '桌号(数字)',
  total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
  discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额',
  final_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
  status VARCHAR(20) DEFAULT 'unpaid' COMMENT '统一状态 unpaid:待支付 paid:已支付 cooking:制作中 completed:已完成 cancelled:已取消',
  payment_method VARCHAR(20) COMMENT '支付方式',
  wechat_prepay_id VARCHAR(100) COMMENT '微信预支付ID',
  wechat_transaction_id VARCHAR(100) COMMENT '微信交易号',
  remark TEXT COMMENT '订单备注',
  -- 预留字段，适配后续升级
  cashback_used DECIMAL(10,2) DEFAULT 0 COMMENT '使用的返现金额',
  payment_status TINYINT DEFAULT 0 COMMENT '支付状态 0:未支付 1:已支付 2:支付失败',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tenant_order_number (tenant_id, order_number),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at),
  INDEX idx_is_valid (is_valid),
  FOREIGN KEY (user_id) REFERENCES small_users(id)
);
```

#### 9. 订单详情表 (small_order_items)
```sql
CREATE TABLE small_order_items (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  order_id BIGINT NOT NULL COMMENT '订单ID',
  item_type TINYINT NOT NULL COMMENT '商品类型 1:单品 2:套餐',
  dish_id BIGINT COMMENT '菜品ID',
  meal_set_id BIGINT COMMENT '套餐ID',
  item_name VARCHAR(100) NOT NULL COMMENT '商品名称',
  item_price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
  quantity INT NOT NULL COMMENT '数量',
  subtotal DECIMAL(10,2) NOT NULL COMMENT '小计金额',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_order_id (order_id),
  INDEX idx_is_valid (is_valid),
  FOREIGN KEY (order_id) REFERENCES small_orders(id),
  FOREIGN KEY (dish_id) REFERENCES small_dishes(id),
  FOREIGN KEY (meal_set_id) REFERENCES small_meal_sets(id)
);
```

#### 10. 优惠券表 (small_coupons) - 预留扩展
```sql
CREATE TABLE small_coupons (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  name VARCHAR(100) NOT NULL COMMENT '优惠券名称',
  type TINYINT NOT NULL COMMENT '类型 1:满减券 2:折扣券 3:新用户券',
  amount DECIMAL(10,2) NOT NULL COMMENT '优惠金额/折扣率',
  min_amount DECIMAL(10,2) DEFAULT 0 COMMENT '最低消费金额',
  total_count INT NOT NULL COMMENT '发放总数',
  used_count INT DEFAULT 0 COMMENT '已使用数量',
  expire_days INT NOT NULL COMMENT '有效天数',
  status TINYINT DEFAULT 1 COMMENT '状态 1:有效 0:无效',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_is_valid (is_valid)
);
```

#### 11. 用户优惠券表 (small_user_coupons) - 预留扩展
```sql
CREATE TABLE small_user_coupons (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  coupon_id BIGINT NOT NULL COMMENT '优惠券ID',
  coupon_name VARCHAR(100) NOT NULL COMMENT '优惠券名称',
  amount DECIMAL(10,2) NOT NULL COMMENT '优惠金额',
  min_amount DECIMAL(10,2) DEFAULT 0 COMMENT '最低消费金额',
  status TINYINT DEFAULT 1 COMMENT '状态 1:未使用 2:已使用 3:已过期',
  expire_date DATE NOT NULL COMMENT '过期日期',
  used_at TIMESTAMP NULL COMMENT '使用时间',
  order_id BIGINT COMMENT '使用订单ID',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_is_valid (is_valid),
  FOREIGN KEY (user_id) REFERENCES small_users(id),
  FOREIGN KEY (coupon_id) REFERENCES small_coupons(id),
  FOREIGN KEY (order_id) REFERENCES small_orders(id)
);
```

#### 12. 返现记录表 (small_cashback_records) - 预留扩展
```sql
CREATE TABLE small_cashback_records (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  user_id BIGINT NOT NULL COMMENT '获得返现的会员ID',
  invitee_id BIGINT NOT NULL COMMENT '被邀请的用户ID',
  order_id BIGINT NOT NULL COMMENT '触发返现的订单ID',
  amount DECIMAL(10,2) NOT NULL COMMENT '返现金额',
  status TINYINT DEFAULT 1 COMMENT '状态 1:已发放 0:已撤销',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_user_id (user_id),
  INDEX idx_invitee_id (invitee_id),
  INDEX idx_is_valid (is_valid),
  FOREIGN KEY (user_id) REFERENCES small_users(id),
  FOREIGN KEY (invitee_id) REFERENCES small_users(id),
  FOREIGN KEY (order_id) REFERENCES small_orders(id)
);
```

#### 13. 会员等级配置表 (small_member_levels) - 预留扩展
```sql
CREATE TABLE small_member_levels (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  level_code TINYINT NOT NULL COMMENT '等级代码 0:普通 1:铜牌 2:银牌 3:金牌',
  level_name VARCHAR(50) NOT NULL COMMENT '等级名称',
  annual_fee DECIMAL(10,2) DEFAULT 0 COMMENT '年费',
  cashback_rate DECIMAL(5,4) DEFAULT 0 COMMENT '返现比例',
  invite_reward DECIMAL(10,2) DEFAULT 0 COMMENT '邀请奖励金额',
  benefits TEXT COMMENT '会员权益描述',
  status TINYINT DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tenant_level_code (tenant_id, level_code),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_is_valid (is_valid)
);
```

### 系统配置表 (无前缀)

#### 14. 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  config_key VARCHAR(100) NOT NULL COMMENT '配置键',
  config_value TEXT COMMENT '配置值',
  description VARCHAR(200) COMMENT '配置描述',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tenant_config_key (tenant_id, config_key),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_is_valid (is_valid)
);
```

#### 15. 公告信息表 (announcements)
```sql
CREATE TABLE announcements (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL COMMENT '租户ID',
  title VARCHAR(200) NOT NULL COMMENT '公告标题',
  content TEXT COMMENT '公告内容',
  type TINYINT DEFAULT 1 COMMENT '公告类型 1:系统公告 2:活动公告 3:维护公告',
  priority TINYINT DEFAULT 1 COMMENT '优先级 1:低 2:中 3:高',
  status TINYINT DEFAULT 1 COMMENT '状态 1:发布 0:草稿',
  start_time TIMESTAMP COMMENT '开始时间',
  end_time TIMESTAMP COMMENT '结束时间',
  is_valid TINYINT DEFAULT 1 COMMENT '逻辑删除 1:有效 0:已删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_is_valid (is_valid)
);
```

## 业务模块梳理 (当前版本)

### 1. 用户认证模块
**功能**: 微信小程序登录、用户信息管理
**涉及表**: `small_users`
**核心流程**:
- 微信小程序登录获取code
- 后端调用微信API获取openid
- 创建/更新用户信息
- 生成自定义JWT Token

### 2. 店铺信息模块
**功能**: 店铺基础信息、推荐菜品
**涉及表**: `small_shops`, `small_dishes`
**核心流程**:
- 获取店铺营业信息
- 展示推荐菜品列表

### 3. 桌号管理模块
**功能**: 数字桌号验证、占用、释放
**涉及表**: `small_tables`
**核心流程**:
- 验证数字桌号有效性(1-12)
- 更新桌号占用状态
- 处理桌号释放

### 4. 菜品管理模块
**功能**: 分类管理、菜品展示
**涉及表**: `small_categories`, `small_dishes`
**核心流程**:
- 获取菜品分类列表
- 按分类获取菜品
- 获取推荐菜品
- 库存状态管理

### 5. 订单管理模块
**功能**: 订单创建、支付、状态跟踪
**涉及表**: `small_orders`, `small_order_items`
**核心流程**:
- 创建订单(单品)
- 生成微信支付参数
- 统一的订单状态管理
- 订单列表按状态和时间排序

### 6. 会员系统模块 (简化版)
**功能**: 会员状态管理、基础优惠券
**涉及表**: `small_users`, `small_coupons`, `small_user_coupons`
**核心流程**:
- 会员状态管理(是会员/非会员)
- 优惠券基础功能

### 7. 支付系统模块
**功能**: 微信支付、支付回调
**涉及表**: `small_orders`
**核心流程**:
- 订单创建时生成微信支付参数
- 支付回调处理（更新订单状态）
- 统一状态管理

### 8. 系统配置模块
**功能**: 系统参数配置
**涉及表**: `system_configs`
**核心流程**:
- 系统参数管理

## API接口设计

### 基础配置
- **Base URL**: `https://api.chuanweixiaochu.com/api/v1`
- **认证方式**: 自定义JWT Token (通过微信小程序登录获取)
- **数据格式**: JSON
- **字符编码**: UTF-8
- **支付**: 微信支付 API v3

### 统一响应格式
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

### 错误码定义
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权/Token无效
- `403`: 禁止访问
- `404`: 资源不存在
- `409`: 资源冲突
- `422`: 业务逻辑错误
- `500`: 服务器内部错误

### 请求头配置
```
Authorization: Bearer {custom_jwt_token}
Content-Type: application/json
```

### 1. 用户认证接口

#### 1.1 微信登录
```
POST /auth/wechat-login
```

**说明**: 小程序调用wx.login()获取code，然后调用此接口完成登录

**请求参数:**
```json
{
  "code": "微信登录code",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  },
  "inviteCode": "邀请码(可选)"
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "custom_jwt_token",
    "userInfo": {
      "id": 1,
      "nickName": "用户昵称",
      "avatarUrl": "头像URL",
      "memberLevel": 1,
      "memberLevelName": "铜牌会员",
      "memberExpireDate": "2024-12-31",
      "cashbackBalance": 128.50,
      "inviteCode": "ABC123",
      "inviteCount": 3
    }
  }
}
```

**说明**:
- 后端收到code后调用微信API获取openid
- 根据openid查询或创建用户
- 处理邀请码逻辑
- 返回自定义JWT token和用户信息

### 2. 店铺信息接口

#### 2.1 获取店铺信息
```
GET /shop/info
```

**说明**: 获取店铺基本信息，对应小程序home页面的shopInfo数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "name": "川味小厨",
    "description": "正宗川菜 · 地道美味",
    "phone": "023-8888-6666",
    "address": "重庆市渝中区美食街88号",
    "businessHours": "09:00 - 22:00",
    "logo": "https://supabase-storage-url/logo.png",
    "status": 1
  }
}
```

#### 2.2 获取推荐菜品
```
GET /shop/recommended-dishes
```

**说明**: 获取首页推荐菜品列表，对应小程序home页面的recommendList数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "宫保鸡丁",
      "price": 28,
      "emoji": "🍗",
      "description": "经典川菜，鸡肉嫩滑，花生香脆",
      "image": "/images/dishes/gongbao.jpg"
    },
    {
      "id": 4,
      "name": "水煮鱼",
      "price": 45,
      "emoji": "🐟",
      "description": "鲜嫩鱼片，麻辣鲜香",
      "image": "/images/dishes/shuizhuyu.jpg"
    }
  ]
}
```

### 3. 桌号管理接口

#### 3.1 验证桌号
```
POST /tables/validate
```

**请求参数:**
```json
{
  "tableNumber": 1
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "valid": true,
    "tableInfo": {
      "id": 1,
      "tableNumber": 1,
      "seats": 4,
      "status": 0
    }
  }
}
```

#### 3.2 占用桌号
```
POST /tables/occupy
```

**请求参数:**
```json
{
  "tableNumber": 1
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "message": "桌号占用成功"
}
```

#### 3.3 释放桌号
```
POST /tables/release
```

**请求参数:**
```json
{
  "tableNumber": 1
}
```

### 4. 菜品管理接口

#### 4.1 获取菜品分类
```
GET /dishes/categories
```

**说明**: 获取菜品分类列表，对应小程序menu页面的categories数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "热菜"
    },
    {
      "id": 2,
      "name": "凉菜"
    },
    {
      "id": 3,
      "name": "汤品"
    },
    {
      "id": 4,
      "name": "川菜"
    },
    {
      "id": 5,
      "name": "素食"
    },
    {
      "id": 6,
      "name": "海鲜"
    },
    {
      "id": 7,
      "name": "烧烤"
    },
    {
      "id": 8,
      "name": "主食"
    },
    {
      "id": 9,
      "name": "饮品"
    },
    {
      "id": 10,
      "name": "甜品"
    }
  ]
}
```

#### 4.2 获取所有菜品
```
GET /dishes/list
```

**说明**: 获取所有菜品数据，对应小程序menu页面的dishes数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "宫保鸡丁",
      "description": "经典川菜，鸡肉嫩滑，花生香脆，酸甜微辣",
      "price": 28,
      "emoji": "🍗",
      "image": "/images/dishes/gongbao.jpg",
      "categoryId": 1,
      "isRecommended": true,
      "count": 0
    },
    {
      "id": 2,
      "name": "麻婆豆腐",
      "description": "嫩滑豆腐配麻辣肉末，口感丰富层次分明",
      "price": 18,
      "emoji": "🥘",
      "image": "/images/dishes/mapo.jpg",
      "categoryId": 1,
      "isRecommended": false,
      "count": 0
    }
  ]
}
```

**说明**:
- `emoji` 字段与前端完全匹配
- `image` 使用相对路径，与前端一致
- `count` 字段默认为0，用于购物车数量管理

#### 4.3 获取推荐菜品 (首页使用)
```
GET /dishes/recommended
```

**说明**: 获取推荐菜品列表，对应小程序home页面的recommendList数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "宫保鸡丁",
      "price": 28,
      "emoji": "🍗",
      "description": "经典川菜，鸡肉嫩滑，花生香脆",
      "image": "/images/dishes/gongbao.jpg"
    },
    {
      "id": 4,
      "name": "水煮鱼",
      "price": 45,
      "emoji": "🐟",
      "description": "鲜嫩鱼片，麻辣鲜香",
      "image": "/images/dishes/shuizhuyu.jpg"
    }
  ]
}
```

**说明**:
- 专门为首页推荐菜品设计的接口
- 返回数据结构与前端完全匹配
- 包含emoji字段和相对路径图片

### 5. 订单管理接口

#### 5.1 创建订单
```
POST /orders/create
```

**说明**: 根据小程序购物车数据创建订单，对应menu页面的checkout()方法

**请求参数:**
```json
{
  "tableNumber": 1,
  "items": [
    {
      "id": 1,
      "name": "宫保鸡丁",
      "description": "经典川菜，鸡肉嫩滑，花生香脆，酸甜微辣",
      "price": 28,
      "emoji": "🍗",
      "image": "/images/dishes/gongbao.jpg",
      "categoryId": 1,
      "isRecommended": true,
      "count": 2
    }
  ],
  "totalPrice": 56,
  "remark": "不要辣"
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "orderId": 1,
    "orderNumber": "20241201123456001",
    "totalAmount": 56.00,
    "finalAmount": 56.00,
    "paymentInfo": {
      "prepayId": "wx_prepay_id_123456",
      "timeStamp": "1640995200",
      "nonceStr": "random_string_abc",
      "package": "prepay_id=wx_prepay_id_123456",
      "signType": "RSA",
      "paySign": "signature_hash"
    }
  }
}
```

**说明:**
- `tableNumber` 使用数字类型，与前端匹配
- items数组直接使用小程序购物车的cartItems数据结构
- 当前版本暂不处理优惠券和返现，简化为直接支付
- 后端根据菜品ID和数量计算总价
- 返回微信支付所需的参数

#### 5.2 获取订单列表
```
GET /orders/list
```

**说明**: 获取用户所有订单列表，按时间倒序，未支付订单置顶，对应profile页面的allOrders数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": "order_1",
      "orderNumber": "241201123456",
      "tableNumber": 1,
      "items": [
        {
          "name": "宫保鸡丁",
          "count": 2,
          "price": 28
        }
      ],
      "totalPrice": 56,
      "finalAmount": 56,
      "status": "unpaid",
      "statusText": "待支付",
      "createTime": 1640995200000,
      "createTimeText": "12-01 12:34",
      "dishNames": "宫保鸡丁×2"
    },
    {
      "id": "order_2",
      "orderNumber": "241130123456",
      "tableNumber": 2,
      "items": [
        {
          "name": "麻婆豆腐",
          "count": 1,
          "price": 18
        }
      ],
      "totalPrice": 18,
      "finalAmount": 18,
      "status": "completed",
      "statusText": "已完成",
      "createTime": 1640909200000,
      "createTimeText": "11-30 15:20",
      "dishNames": "麻婆豆腐×1"
    }
  ]
}
```

**说明:**
- `tableNumber` 使用数字类型，与前端匹配
- 返回所有订单，未支付订单在前，已支付订单按时间倒序
- `status`: unpaid=待支付, paid=已支付, cooking=制作中, completed=已完成, cancelled=已取消
- 统一状态管理，移除独立的paymentStatus字段

#### 5.3 支付未支付订单
```
POST /orders/{orderId}/pay
```

**说明**: 对未支付订单进行支付，对应profile页面的"去支付"按钮

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "orderId": 1,
    "orderNumber": "20241201123456001",
    "finalAmount": 46.00,
    "paymentInfo": {
      "prepayId": "wx_prepay_id_123456",
      "timeStamp": "1640995200",
      "nonceStr": "random_string_abc",
      "package": "prepay_id=wx_prepay_id_123456",
      "signType": "RSA",
      "paySign": "signature_hash"
    }
  }
}
```

#### 5.4 获取订单详情
```
GET /orders/{orderId}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "id": 1,
    "orderNumber": "20241201123456001",
    "tableNumber": "A01",
    "totalAmount": 56.00,
    "discountAmount": 10.00,
    "cashbackUsed": 0.00,
    "finalAmount": 46.00,
    "status": "unpaid",
    "statusText": "待支付",
    "paymentStatus": 0,
    "paymentMethod": null,
    "wechatTransactionId": null,
    "remark": "不要辣",
    "createdAt": "2024-12-01T12:34:56Z",
    "items": [
      {
        "id": 1,
        "itemName": "宫保鸡丁",
        "itemPrice": 28.00,
        "quantity": 2,
        "subtotal": 56.00
      }
    ]
  }
}
```

### 6. 会员系统接口

#### 6.1 获取用户信息
```
GET /users/profile
```

**说明**: 获取用户完整信息，对应profile页面的userInfo和memberInfo数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "userInfo": {
      "nickName": "川菜达人",
      "avatarUrl": "/images/default-avatar.png"
    },
    "memberInfo": {
      "isMember": true,
      "levelName": "会员",
      "expireDate": "2024.12.31"
    },
    "inviteCount": 3,
    "inviteCode": "ABC123"
  }
}
```

**说明**:
- 简化会员系统为二元状态：`isMember` true/false
- 移除复杂的会员等级和返现金额
- 保留基础的邀请功能

#### 6.2 获取用户优惠券
```
GET /users/coupons
```

**说明**: 获取用户优惠券列表，对应profile页面的coupons数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "新用户专享",
      "amount": 10,
      "minAmount": 50,
      "expireDate": "2024.12.31"
    },
    {
      "id": 2,
      "name": "会员专享",
      "amount": 20,
      "minAmount": 100,
      "expireDate": "2024.12.31"
    }
  ]
}
```

#### 6.3 获取邀请记录
```
GET /users/invite-records
```

**说明**: 获取用户邀请记录，对应profile页面的inviteList数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "张小明",
      "inviteTime": "2024-11-25 14:30",
      "reward": 50
    },
    {
      "id": 2,
      "name": "李小红",
      "inviteTime": "2024-11-20 09:15",
      "reward": 50
    },
    {
      "id": 3,
      "name": "王小华",
      "inviteTime": "2024-11-18 16:45",
      "reward": 50
    }
  ]
}
```

#### 6.4 购买会员
```
POST /users/purchase-membership
```

**请求参数:**
```json
{
  "memberLevel": 3,
  "duration": 12
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "orderId": 2,
    "orderNumber": "20241201123456002",
    "amount": 99.00,
    "paymentInfo": {
      "prepayId": "wx_prepay_id_membership",
      "timeStamp": "1640995200",
      "nonceStr": "random_string_def",
      "package": "prepay_id=wx_prepay_id_membership",
      "signType": "RSA",
      "paySign": "signature_hash_membership"
    }
  }
}
```

#### 6.5 验证邀请码
```
POST /users/validate-invite-code
```

**请求参数:**
```json
{
  "inviteCode": "ABC123"
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "valid": true,
    "inviterInfo": {
      "nickname": "川菜达人",
      "memberLevel": 3
    }
  }
}
```

### 7. 支付系统接口

#### 7.1 微信支付回调
```
POST /payments/wechat/notify
```

**请求说明:**
- 微信支付服务器回调接口
- 需要验证微信签名
- 处理支付成功/失败状态
- 更新订单支付状态
- 处理会员拉新返现逻辑

**回调处理逻辑:**
1. 验证微信支付签名
2. 更新订单支付状态（payment_status = 1）
3. 更新订单状态（status = 'pending'，进入待处理状态）
4. 如果是新用户首次消费，给邀请人发放返现
5. 返回微信要求的响应格式

#### 7.2 查询支付状态
```
GET /payments/status/{orderNumber}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "orderNumber": "20241201123456001",
    "paymentStatus": 1,
    "paymentMethod": "wechat",
    "wechatTransactionId": "wx_transaction_123456",
    "paidAt": "2024-12-01T12:35:30Z"
  }
}
```

#### 7.3 申请退款
```
POST /payments/refund
```

**请求参数:**
```json
{
  "orderNumber": "20241201123456001",
  "refundAmount": 84.00,
  "reason": "用户取消订单"
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "refundId": "refund_123456",
    "refundStatus": "processing",
    "refundAmount": 84.00
  }
}
```

## 前端数据适配

### Supabase客户端配置
```typescript
// utils/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://your-project.supabase.co'
const supabaseAnonKey = 'your-anon-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

### API服务封装
```typescript
// utils/api.ts
class ApiService {
  private baseURL = 'https://api.chuanweixiaochu.com/api/v1'

  private async request(url: string, options: any = {}) {
    const token = await this.getSupabaseToken()

    return wx.request({
      url: `${this.baseURL}${url}`,
      method: options.method || 'GET',
      data: options.data,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.header
      }
    })
  }

  private async getSupabaseToken() {
    const { data: { session } } = await supabase.auth.getSession()
    return session?.access_token || ''
  }

  // 微信登录
  async wechatLogin(code: string, userInfo: any, inviteCode?: string) {
    return this.request('/auth/wechat-login', {
      method: 'POST',
      data: { code, userInfo, inviteCode }
    })
  }

  // 获取店铺信息
  async getShopInfo() {
    return this.request('/shop/info')
  }

  // 创建订单
  async createOrder(orderData: any) {
    return this.request('/orders/create', {
      method: 'POST',
      data: orderData
    })
  }
}

export const apiService = new ApiService()
```

### 渐进式接口集成策略
1. **第一阶段**: 集成用户认证和基础数据接口
2. **第二阶段**: 集成菜品、套餐和购物车接口
3. **第三阶段**: 集成订单和支付接口
4. **第四阶段**: 集成会员系统和返现功能

## Spring Boot 后端实现要点

### 依赖配置 (pom.xml)
```xml
<dependencies>
    <!-- Spring Boot 3.x -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- Supabase PostgreSQL -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
    </dependency>

    <!-- 微信支付 -->
    <dependency>
        <groupId>com.github.wechatpay-apiv3</groupId>
        <artifactId>wechatpay-java</artifactId>
        <version>0.2.12</version>
    </dependency>

    <!-- JWT -->
    <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
        <version>0.11.5</version>
    </dependency>
</dependencies>
```

### 核心配置
```yaml
# application.yml
spring:
  datasource:
    url: ***********************************************************
    username: postgres
    password: your-password
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

supabase:
  url: https://your-project.supabase.co
  anon-key: your-anon-key
  service-role-key: your-service-role-key

wechat:
  pay:
    app-id: your-wechat-app-id
    mch-id: your-merchant-id
    private-key-path: classpath:wechat-pay-private-key.pem
    merchant-serial-number: your-serial-number
    api-v3-key: your-api-v3-key
```

## 部署建议

### 开发环境
- 使用Supabase本地开发环境
- 微信支付沙箱环境测试
- 本地Spring Boot应用调试

### 生产环境
- **后端**: 部署到云服务器 (阿里云/腾讯云)
- **数据库**: Supabase云端PostgreSQL
- **文件存储**: Supabase Storage
- **CDN**: 加速图片和静态资源
- **监控**: 集成应用性能监控
- **安全**: HTTPS + API限流 + 参数验证

## 当前版本设计重点

### 核心功能实现
1. **数字桌号管理**: 简化为1-12号桌，便于用户选择和管理
2. **菜品展示**: 保留emoji字段，提升用户体验
3. **单品点餐**: 专注核心点餐流程，暂不实现套餐功能
4. **简化会员**: 二元状态管理，降低系统复杂度
5. **统一订单状态**: 简化支付和订单状态管理

### 数据库设计策略
- **保持完整性**: 数据库表结构保留完整字段，适配后续升级
- **预留扩展**: 复杂功能字段标记为预留，便于后续开发
- **向前兼容**: 当前简化实现不影响未来功能扩展

## 扩展功能规划

### 第二阶段 (后续版本)
1. **套餐功能**: 启用套餐管理和组合优惠
2. **会员等级**: 实现多级会员体系和返现机制
3. **优惠券系统**: 完善优惠券发放和使用逻辑
4. **营销活动**: 满减、折扣券、限时优惠

### 第三阶段 (长期规划)
1. **数据统计**: 销售报表、热门菜品分析
2. **智能推荐**: 基于用户偏好的菜品推荐
3. **多店铺支持**: 连锁店管理
4. **外卖配送**: 配送地址、配送费计算

---

*本文档版本: v2.1 (简化版)*
*最后更新: 2024-12-05*
*设计策略: 当前简化实现，数据库预留扩展*
*技术栈: Java 17 + Spring Boot 3.x + Supabase*
*维护人员: 开发团队*
