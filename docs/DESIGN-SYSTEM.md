# 川味小厨小程序设计系统

## 设计理念

**高端餐饮风格** - 体现川菜的自然品质感，简约美观大方，摒弃童话风格

## 核心色彩系统

### 主色调
- **深绿色**: `#27AE60` - 自然品质，代表新鲜食材
- **金色**: `#F1C40F` - 高端点缀，体现品质感
- **奶白色**: `#FEFEFE` - 主背景色，优雅纯净

### 辅助色彩
- **浅绿色**: `#E8F5E8` - 辅助背景，呼应主色调
- **深色文字**: `#2C3E50` - 主要文字颜色
- **柔和灰色**: `#7F8C8D` - 次要文字颜色
- **浅灰色**: `#95A5A6` - 辅助文字颜色

### 功能色彩
- **成功色**: `#27AE60` - 统一使用主色调
- **会员色**: `#F39C12` - 温暖金色
- **警告色**: `#F39C12` - 橙色系
- **信息色**: `#3498DB` - 蓝色系

## 设计原则

### 1. 简约优雅
- 减少不必要的装饰元素
- 统一的圆角设计（8-12rpx）
- 柔和的阴影效果

### 2. 层次分明
- 清晰的视觉层次
- 合理的间距设计
- 统一的字重系统

### 3. 现代感
- 去除过度的渐变效果
- 使用现代化的色彩搭配
- 优化的交互反馈

## 组件样式规范

### 按钮
- 主按钮：深绿色背景 + 白色文字
- 次按钮：金色背景 + 深色文字
- 圆角：8rpx
- 阴影：柔和投影效果

### 卡片
- 背景：纯白色
- 圆角：12rpx
- 边框：浅灰色细边框
- 阴影：轻微投影

### 文字
- 主标题：34rpx，字重600
- 副标题：26rpx，字重500
- 正文：28rpx，字重400

## 交互效果

### 悬停状态
- 轻微的背景色变化
- 柔和的过渡动画（0.3s ease）

### 激活状态
- 微妙的缩放效果（scale 0.98-1.05）
- 轻微的位移效果（1-2rpx）

### 选中状态
- 主色调边框
- 淡色背景填充
- 金色指示器

## 应用场景

### 导航栏
- 背景：深绿色渐变
- 文字：白色
- 阴影：柔和投影

### 底部标签栏
- 背景：奶白色半透明
- 未选中：柔和灰色
- 选中：深绿色 + 金色指示器

### 内容区域
- 背景：奶白色
- 卡片：白色背景 + 柔和阴影
- 分割线：浅灰色

## 品质感体现

1. **色彩搭配**: 深绿 + 金色的经典高端组合
2. **材质感**: 毛玻璃效果 + 柔和阴影
3. **细节处理**: 统一的圆角 + 精致的间距
4. **交互反馈**: 微妙的动画效果

## 与川菜文化的结合

- **绿色**: 代表新鲜食材和自然品质
- **金色**: 体现川菜的丰富层次和高端品质
- **简约设计**: 突出菜品本身的魅力
- **温暖色调**: 营造舒适的用餐氛围
